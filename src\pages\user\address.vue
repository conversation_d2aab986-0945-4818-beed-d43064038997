<template>
  <div class="flex-1 bg-white h-full rounded py-4">
    <div class="px-5 mb-4 flex justify-between">
      <span class="text-xl font-medium">{{
        authStore.i18n("cm_addr.deliveryAddress")
      }}</span>
      <span
        class="text-[#0078FF] mr-4 mt-2 cursor-pointer"
        @click="onOpenAddAddr()"
        >{{ authStore.i18n("cm_addr.addAddress") }}</span
      >
    </div>
    <n-data-table
      max-height="62vh"
      :columns="columns"
      :data="pageData.addressList"
      :empty="authStore.i18n('cm_address.noData')"
    >
      <template #empty>
        <n-empty :description="authStore.i18n('cm_address.noData')">
        </n-empty></template
    ></n-data-table>
    <n-modal
      preset="dialog"
      :show-icon="false"
      :style="{
        width: '90%',
        overflow: 'auto',
        maxWidth: '900px',
        padding: '20px 30px',
      }"
      v-model:show="pageData.dialogVisible"
      :on-close="onCloseAddAddr"
      :on-esc="onCloseAddAddr"
      :closable="true"
      :mask-closable="false"
      :title="authStore.i18n('cm_addr.addOrEdit')"
    >
      <n-form
        :model="editForm"
        ref="editFormRef"
        :rules="rules"
        label-width="200px"
        label-placement="left"
        class="mt-10"
      >
        <!-- 联系人 -->
        <n-form-item
          path="contactName"
          :label="authStore.i18n('cm_addr.contact')"
        >
          <n-input
            v-trim
            clearable
            style="width: 80%"
            v-model:value="editForm.contactName"
            :placeholder="authStore.i18n('cm_addr.contactPh')"
          ></n-input>
        </n-form-item>
        <!-- 国家 -->
        <n-form-item
          path="countryId"
          :label="authStore.i18n('cm_addr.country')"
        >
          <n-select
            clearable
            filterable
            style="width: 80%"
            value-field="id"
            label-field="countryEsName"
            :options="pageData.countryList"
            v-model:value="editForm.countryId"
            @update:value="onSelectCountry('update')"
            :placeholder="authStore.i18n('cm_addr.countryPh')"
          />
        </n-form-item>
        <!-- WhatsApp -->
        <n-form-item path="phone" label="WhatsApp">
          <n-input-group style="width: 80%">
            <n-input
              v-trim
              readonly
              class="!w-24"
              v-model:value="editForm.areaCode"
              placeholder="+000"
            />
            <n-input
              v-trim
              clearable
              :input-props="{ type: 'number' }"
              v-model:value="editForm.phone"
              :placeholder="authStore.i18n('cm_search.pleaseInputWhatsapp')"
            />
          </n-input-group>
        </n-form-item>
        <!-- 邮政编码 -->
        <n-form-item
          :label="authStore.i18n('cm_addr.postcode')"
          path="postcode"
        >
          <n-input
            v-trim
            clearable
            style="width: 80%"
            v-model:value="editForm.postcode"
            :input-props="{ type: 'number' }"
            :placeholder="authStore.i18n('cm_addr.postcodePh')"
          ></n-input>
        </n-form-item>
        <!-- 州/城市 -->
        <n-form-item
          path="provinceCity"
          :label="authStore.i18n('cm_addr.province')"
        >
          <!-- 省 -->
          <n-select
            tag
            filterable
            style="width: 39%"
            value-field="name"
            label-field="name"
            children-field="child"
            :options="pageData.provinceList"
            v-model:value="editForm.province"
            @update:value="onSelectProvince('update')"
            :placeholder="authStore.i18n('cm_addr.provincePh')"
          >
          </n-select>
          <!-- 市 -->
          <n-select
            tag
            filterable
            value-field="name"
            label-field="name"
            :options="pageData.cityList"
            v-model:value="editForm.city"
            style="width: 39%; margin-left: 1rem"
            :placeholder="authStore.i18n('cm_addr.cityPh')"
          >
          </n-select>
        </n-form-item>
        <!-- 区 -->
        <n-form-item
          :label="authStore.i18n('cm_addr.regionCode')"
          path="region"
        >
          <n-input
            v-trim
            clearable
            style="width: 80%"
            v-model:value="editForm.region"
            :placeholder="authStore.i18n('cm_addr.regionCodePh')"
          ></n-input>
        </n-form-item>
        <!-- 街道 -->
        <n-form-item :label="authStore.i18n('cm_addr.street')" path="street">
          <n-input
            v-trim
            clearable
            style="width: 80%"
            v-model:value="editForm.street"
            :placeholder="authStore.i18n('cm_addr.streetPh')"
          ></n-input>
        </n-form-item>
        <!-- 详细地址 -->
        <n-form-item :label="authStore.i18n('cm_addr.address')" path="address">
          <n-input
            v-trim
            clearable
            style="width: 80%"
            v-model:value="editForm.address"
            :placeholder="authStore.i18n('cm_addr.addressPh')"
          ></n-input>
        </n-form-item>
        <!-- 门牌号 -->
        <n-form-item :label="authStore.i18n('cm_addr.houseNo')">
          <n-input
            v-trim
            clearable
            style="width: 80%"
            v-model:value="editForm.houseNo"
            :placeholder="authStore.i18n('cm_addr.houseNoPh')"
          ></n-input>
        </n-form-item>
        <!-- 参考地标 -->
        <n-form-item :label="authStore.i18n('cm_addr.referLandmark')">
          <n-input
            v-trim
            clearable
            style="width: 80%"
            v-model:value="editForm.referLandmark"
            :placeholder="authStore.i18n('cm_addr.referLandmarkPh')"
          ></n-input>
        </n-form-item>
        <!-- 地址标签 -->
        <n-form-item :label="authStore.i18n('cm_addr.addressLabel')">
          <n-radio-group v-model:value="editForm.addressLabel">
            <n-radio-button value="ADDRESS_LABEL_HOME">{{
              authStore.i18n("cm_addr.home")
            }}</n-radio-button>
            <n-radio-button value="ADDRESS_LABEL_COMPANY">{{
              authStore.i18n("cm_addr.business")
            }}</n-radio-button>
          </n-radio-group>
        </n-form-item>
        <n-form-item label=" ">
          <n-checkbox
            :label="authStore.i18n('cm_addr.defaultFlag')"
            v-model:checked="editForm.isDefault"
          ></n-checkbox>
        </n-form-item>
      </n-form>
      <template #action>
        <n-button @click="onCloseAddAddr">{{
          authStore.i18n("cm_addr.cancelBtn")
        }}</n-button>
        <n-button type="primary" @click="onAddAddr">{{
          authStore.i18n("cm_addr.confirmBtn")
        }}</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import {
  NTag,
  NSpace,
  NButton,
  NDataTable,
  useMessage,
  NPopconfirm,
  type DataTableColumns,
} from "naive-ui";
import type { FormInst, FormRules, FormItemRule } from "naive-ui";

const message = useMessage();
const authStore = useAuthStore();
const editForm = reactive(<any>{});
const editFormRef = ref<FormInst | null>(null);
const pageData = reactive(<any>{
  addressList: <any>[],
  countryList: <any>[],
  provinceList: <any>[],
  cityList: <any>[],
  dialogVisible: false,
  countryRegexes: <any>{},
});

const AddressLabel = {
  ADDRESS_LABEL_HOME: authStore.i18n("cm_addr.home"), //家
  ADDRESS_LABEL_COMPANY: authStore.i18n("cm_addr.business"), //公司
};

const columns: DataTableColumns<any> = [
  {
    title: authStore.i18n("cm_addr.contactName"),
    key: "contactName",
    width: 160,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: authStore.i18n("cm_addr.phone"),
    key: "phone",
    width: 120,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: authStore.i18n("cm_addr.fullAddress"),
    key: "fullAddress",
    width: 220,
  },
  {
    title: authStore.i18n("cm_addr.addressLabel"),
    key: "addressLabel",
    width: 120,
    render(row: any) {
      if (!row?.addressLabel) {
        return null;
      }

      return h(
        NTag,
        {
          bordered: false,
          type:
            "ADDRESS_LABEL_HOME" === row?.addressLabel ? "warning" : "default",
        },
        {
          default: () =>
            AddressLabel[row.addressLabel as keyof typeof AddressLabel],
        }
      );
    },
  },
  {
    title: authStore.i18n("cm_addr.isDefault"),
    key: "isDefault",
    width: 140,
    render(row: any) {
      return h(
        NTag,
        {
          bordered: false,
          type: row.isDefault ? "info" : "default",
        },
        {
          default: () =>
            row?.isDefault
              ? authStore.i18n("cm_addr.yes")
              : authStore.i18n("cm_addr.no"),
        }
      );
    },
  },
  {
    title: authStore.i18n("cm_addr.operation"),
    key: "action",
    width: 240,
    ellipsis: {
      tooltip: true,
    },
    render(row: any, index) {
      return h(
        NSpace,
        {
          vertical: false,
        },
        onAddressBtn(row, index)
      );
    },
  },
];

const rules: FormRules = {
  contactName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.contactPh"),
  },
  phone: {
    required: true,
    trigger: "blur",
    message: (() => {
      const phoneCountMessage = pageData.countryRegexes?.phoneCount
        ? `WhatsApp ${authStore.i18n("cm_submit.whatsappTips")} ${
            pageData.countryRegexes?.phoneCount
          } ${authStore.i18n("cm_submit.whatsapp")}`
        : authStore.i18n("cm_submit.whatsappRequired");
      return `${phoneCountMessage}`;
    })(),
    validator(rule: FormItemRule, value: any) {
      const lengths =
        pageData.countryRegexes.phoneCount &&
        pageData.countryRegexes.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.countryPh"),
  },
  postcode: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.postcodePh"),
  },
  provinceCity: {
    required: true,
    trigger: "change",
    validator(rule: FormItemRule, value: any) {
      if (!editForm.province) {
        return new Error(authStore.i18n("cm_addr.provincePh"));
      }
      if (!editForm.city) {
        return new Error(authStore.i18n("cm_addr.cityPh"));
      }
      return true;
    },
  },
  region: {
    required: true,
    trigger: "change",
    message: authStore.i18n("cm_addr.regionCodePh"),
  },
  street: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.streetPh"),
  },
  address: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.addressPh"),
  },
};

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onListUserAddress();
onGetCountry();

async function onListUserAddress() {
  const res: any = await useListUserAddress({});
  if (res?.result?.code === 200) {
    pageData.addressList = res?.data;
  } else if (res?.result?.code === 403) {
    window.location.href = "/";
  }
}

async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
  }
}

function onAddressBtn(row?: any, index?: any) {
  return [
    h(
      NButton,
      {
        text: true,
        textColor: "#0078FF",
        onClick: () => {
          onOpenAddAddr(row);
        },
      },
      {
        default: () => authStore.i18n("cm_addr.editAddr"),
      }
    ),
    h(
      NPopconfirm,
      {
        positiveText: authStore.i18n("cm_addr.confirmBtn"),
        negativeText: authStore.i18n("cm_addr.cancelBtn"),
        onPositiveClick: () => onDelAddr(row),
      },
      {
        trigger: () =>
          h(
            NButton,
            {
              text: true,
              textColor: "#e50113",
              onClick: () => {
                window?.MyStat?.addPageEvent(
                  "address_open_delete",
                  "打开地址删除确认框"
                ); // 埋点
              },
            },
            {
              default: () => authStore.i18n("cm_addr.delAddr"),
            }
          ),
        default: () => authStore.i18n("cm_addr.delAddrTip"),
      }
    ),
    !row.isDefault &&
      h(
        NButton,
        {
          text: true,
          textColor: "#0078FF",
          onClick: () => {
            onAddrToDefault(row);
          },
        },
        {
          default: () => authStore.i18n("cm_addr.editAddrDefault"),
        }
      ),
  ];
}

async function onOpenAddAddr(val?: any) {
  Object.keys(editForm).forEach((key) => {
    delete editForm[key];
  });

  // 重置区号和国家校验规则
  editForm.areaCode = null;
  pageData.countryRegexes = {};
  if (!pageData.countryRegexes?.phoneCount) {
    (rules["phone"] as any).message = authStore.i18n(
      "cm_submit.whatsappRequired"
    );
  }

  let eventName, remark;
  if (val) {
    Object.assign(editForm, val);
    // 优先处理邮政编码和省份选择
    if (editForm.countryId) {
      // if (editForm.postcode) {
      //   await onInputPostcode();
      // } else {
      await onSelectCountry();
      // }
      if (pageData.provinceList && editForm.province) {
        onSelectProvince();
      }
    }
    eventName = "address_open_edit";
    remark = "打开地址编辑窗口";
  } else {
    eventName = "address_open_add";
    remark = "打开地址添加窗口";
  }
  window?.MyStat?.addPageEvent(eventName, remark); // 埋点

  if (!editForm.addressLabel) {
    editForm.addressLabel = "ADDRESS_LABEL_HOME";
  }
  pageData.dialogVisible = true;
}

function onCloseAddAddr() {
  pageData.dialogVisible = false;
}

function setCountryData(country: any) {
  editForm.areaCode = country.areaCode;
  pageData.countryRegexes = country;
  if (pageData.countryRegexes?.phoneCount) {
    (rules["phone"] as any).message = `WhatsApp ${authStore.i18n(
      "cm_submit.whatsappTips"
    )} ${pageData.countryRegexes.phoneCount} ${authStore.i18n(
      "cm_submit.whatsapp"
    )}`;
  }
}

async function onSelectCountry(type?: any) {
  const selectedCountry = pageData.countryList.find(
    (country: any) => country.id === editForm.countryId
  );
  if (selectedCountry) {
    setCountryData(selectedCountry);
  }

  const res: any = await useListRegionByCountry({ id: editForm.countryId });
  if (res?.result?.code === 200) {
    onHandleRegion(res?.data, type);
  } else {
    onHandleRegion([], type);
    message.error(res.result?.message);
  }
}

async function onInputPostcode(type?: any) {
  const res: any = await useListRegionByPostcode({
    countryId: editForm.countryId,
    postcode: editForm.postcode,
  });
  if (res?.result?.code === 200) {
    onHandleRegion(res?.data, type);
  } else {
    onHandleRegion([], type);
    message.error(res.result?.message);
  }
}

function onHandleRegion(data: any, type?: any) {
  pageData.provinceList = data;
  pageData.cityList = [];
  if (type === "update") {
    editForm.province = "";
    editForm.city = "";
  }
}

function onSelectProvince(type?: any) {
  const matchProvince = pageData.provinceList.find(
    (item: any) => item.name === editForm.province
  );
  pageData.cityList = matchProvince?.children;
  if (type === "update") {
    editForm.city = "";
  }
}

async function onAddAddr() {
  await editFormRef.value?.validate();
  const matchProvince = pageData.provinceList.find(
    (item: any) => item.name === editForm.province
  );
  const matchCity = matchProvince?.children?.find(
    (item: any) => item.name === editForm.city
  );

  const provinceCode = matchProvince?.code;
  const cityCode = matchCity?.code;
  let params = {
    ...editForm,
    addressLabel: editForm.addressLabel === "ADDRESS_LABEL_HOME" ? 0 : 1,
    cityCode,
    provinceCode,
  };
  const res: any = await useSaveUserAddress(params);
  if (res?.result?.code === 200) {
    onCloseAddAddr();
    onListUserAddress();
    const isEdit = Boolean(editForm.id);
    const successMessage = isEdit
      ? authStore.i18n("cm_addr.editSuccess")
      : authStore.i18n("cm_addr.addSuccess");

    const eventName = isEdit ? "address_save_edit" : "address_save_add";
    const remark = isEdit ? "地址编辑保存成功" : "地址添加保存成功";

    message.success(successMessage); //成功提示
    window?.MyStat?.addPageEvent(eventName, remark); //埋点
  } else {
    message.error(res.result?.message);
  }
}

async function onDelAddr(val: any) {
  let params = {
    id: val.id,
  };
  const res: any = await useDeleteUserAddress(params);
  if (res?.result?.code === 200) {
    onListUserAddress();
    message.success(authStore.i18n("cm_addr.delSuccess"));
    window?.MyStat?.addPageEvent("address_save_delete", "地址删除保存成功"); // 埋点
  } else {
    message.error(res.result?.message);
  }
}

async function onAddrToDefault(val: any) {
  let params = {
    id: val.id,
  };
  const res: any = await useAddressToDefault(params);
  if (res?.result?.code === 200) {
    onListUserAddress();
    message.success(authStore.i18n("cm_addr.editSuccess"));
  } else {
    message.error(res.result?.message);
  }
}
</script>
<style scoped lang="scss">
:deep(.n-input) {
  --n-caret-color: #e50113 !important;
  --n-border-hover: 1px solid #e50113 !important;
  --n-border-focus: 1px solid #e50113 !important;
}
:deep(input::-webkit-outer-spin-button),
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none;
}

:deep(input[type="number"]) {
  -moz-appearance: textfield;
}

:deep(.n-data-table .n-data-table-th) {
  background-color: #fff;
  font-weight: 500;
}
</style>
