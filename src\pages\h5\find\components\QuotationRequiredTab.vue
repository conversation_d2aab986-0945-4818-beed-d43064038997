<template>
  <div>
    <div
      class="flex justify-between items-center py-[0.36rem] px-[0.24rem] border-b-1 border-[#F2F2F2]"
    >
      <div
        class="text-[#1EA62A] text-[0.36rem] leading-[0.36rem] font-medium"
        :class="props.showTabs ? '!text-[#1B80E4]' : 'text-[#333]'"
      >
        {{ authStore.i18n("cm_find.inquiryList") }}
        <span class="text-[0.28rem] leading-[0.28rem] ml-[0.08rem]">
          ({{ cartData.stat?.goodsCount || 0 }})
        </span>
      </div>
      <div class="flex items-center">
        <img
          loading="lazy"
          alt="back"
          class="w-[0.24rem] mr-[0.08rem]"
          src="@/assets/icons/common/address.svg"
          referrerpolicy="no-referrer"
        />
        <mobile-country-select
          @save="onSaveCountry"
          spm="select_site_from_cart"
        />
      </div>
    </div>
    <!-- 商品列表区域 -->
    <div class="bg-white rounded-[0.16rem] p-[0.16rem] mb-[0.16rem]">
      <div v-if="cartData.goodsList.length">
        <!-- 二级 -->
        <div
          v-for="(goods, index) in cartData.goodsList"
          :key="goods.goodsId"
          class="mb-[0.5rem]"
        >
          <div class="flex items-center w-full">
            <n-checkbox
              class="mr-[0.16rem]"
              v-model:checked="goods.selected"
              @update:checked="
                (value) => $emit('onGoodsSelection', value, goods)
              "
            >
            </n-checkbox>
            <a
              :href="`/h5/goods/${goods.goodsId}${
                goods.padc ? `?padc=${goods.padc}` : ''
              }`"
              data-spm-box="cart-goods-list"
              :data-spm-index="index + 1"
            >
              <mobile-goods-card
                :goods="goods"
                class="flex-1"
                spmCode="cart-goods-list"
                ><icon-card
                  name="uil:trash-alt"
                  color="#797979"
                  size="0.4rem"
                  class="mx-[0.16rem]"
                  @click.stop.prevent="$emit('onDeleteGoods', goods)"
                ></icon-card
              ></mobile-goods-card>
            </a>
          </div>
          <!-- 三级分类 -->
          <div
            v-for="sku in goods.skuList"
            :key="sku.skuId"
            class="mb-[0.24rem]"
          >
            <div class="flex items-center">
              <n-checkbox
                class="mr-[0.16rem]"
                v-model:checked="sku.selected"
                @update:checked="
                  (value) => $emit('onSkuSelection', value, sku, goods)
                "
              >
              </n-checkbox>
              <mobile-sku-card
                :sku="sku"
                :goods="goods"
                @onCartQtyUpdate="
                  (value) => $emit('onCartQtyUpdate', value, sku, goods)
                "
                :step="sku.minIncreaseQuantity"
                ><template v-slot:spec>
                  <icon-card
                    name="iconamoon:arrow-right-2"
                    color="#797979"
                    size="0.45rem"
                    @click.stop="$emit('onOpenSkuDialog', sku, goods)"
                  ></icon-card>
                </template>
                <template v-slot:delete>
                  <icon-card
                    name="uil:trash-alt"
                    color="#797979"
                    size="0.4rem"
                    @click="$emit('onDeleteSku', sku, goods)"
                  >
                  </icon-card>
                </template>
              </mobile-sku-card>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else>
        <n-empty
          :description="authStore.i18n('cm_find.noData')"
          class="mt-[1.92rem] text-[0.28rem]"
        >
          <template #extra>
            <a href="/h5">
              <n-button
                size="small"
                color="#E50113"
                text-color="#fff"
                class="text-[0.28rem] h-[0.56rem] px-[0.2rem]"
              >
                {{ authStore.i18n("cm_find.goHome") }}
              </n-button>
            </a>
          </template>
        </n-empty>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <!-- 底部信息 -->
    <div
      class="w-full fixed bottom-[1.32rem] bg-white pt-[0.24rem] pb-[0.32rem] px-[0.16rem]"
    >
      <div>
        <div class="flex items-start justify-between">
          <!-- 一级 -->
          <n-checkbox
            v-model:checked="pageData.selectAll"
            @update:checked="onAllSelection"
            ><span class="text-[0.32rem] leading-[0.32rem]">{{
              authStore.i18n("cm_find_selectAll")
            }}</span></n-checkbox
          >
          <div class="flex gap-[0.08rem] items-center">
            <span class="text-[0.28rem] leading-[0.28rem]">
              {{ authStore.i18n("cm_find_itemsCost") }}:
            </span>
            <div
              class="text-[0.36rem] leading-[0.36rem] font-medium flex-shrink-0 flex-1"
              v-if="
                cartData.stat.selectTotalSalePrice ||
                cartData.stat.selectTotalSalePrice === 0
              "
            >
              <span
                class="text-[0.32rem] leading-[0.32rem] font-medium mr-[0.04rem]"
              >
                {{ monetaryUnit }} </span
              >{{ setNewUnit(cartData.stat.selectTotalSalePrice, true) }}
            </div>
            <icon-card
              color="#555"
              name="ri:arrow-down-s-line"
              size="26"
              @click="openInquireTip"
              class="hidden"
            />
          </div>
        </div>
        <div
          class="flex text-[0.28rem] leading-[0.28rem] items-center justify-end mt-[0.12rem] text-[#7F7F7F]"
        >
          <n-popover trigger="hover" raw>
            <template #trigger>
              <div
                class="cursor-pointer flex-shrink-0 mr-[0.06rem] text-[#7F7F7F] flex items-center"
              >
                <img
                  class="w-[0.28rem] h-[0.28rem] mr-[0.04rem]"
                  src="@/assets/icons/common/alert-circle.svg"
                  :alt="authStore.i18n('cm_goods.estimatedShippingCost')"
                  referrerpolicy="no-referrer"
                />
                {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
              </div>
            </template>
            <div
              style="
                z-index: 1;
                width: 300px;
                padding: 6px 14px;
                background-color: #fff4d4;
                transform-origin: inherit;
                border: 1px solid #f7ba2a;
              "
            >
              {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
            </div>
          </n-popover>

          <span class="flex-shrink-0" v-if="pageData?.totalEstimateFreight">{{
            setUnit(pageData.totalEstimateFreight)
          }}</span>
          <span v-else class="flex-shrink-0">{{
            authStore.i18n("cm_goods.pendingConfirmation")
          }}</span>
        </div>
      </div>
      <n-button
        block
        size="large"
        color="#E50113"
        text-color="#fff"
        class="rounded-[0.16rem] h-[0.92rem] mt-[0.32rem]"
        @click="onGoFindSubmit($event)"
        data-spm-box="cart-to-checkout"
      >
        <div>
          <div class="text-[0.32rem] leading-[0.32rem] font-medium">
            {{ authStore.i18n("cm_find.inquireNow") }}
          </div>
          <div class="text-[0.28rem] leading-[0.28rem] mt-[0.04rem]">
            {{ authStore.i18n("cm_find_confirmWithoutPay") }}
          </div>
        </div>
      </n-button>
    </div>
  </div>
  <n-drawer
    v-model:show="pageData.showTipDrawer"
    width="100%"
    placement="bottom"
    :trap-focus="false"
    default-height="4.4rem"
  >
    <n-drawer-content closable>
      <template #header>
        <div
          class="text-[0.32rem] leading-[0.48rem] font-medium mb-[0.32rem] pt-[0.32rem]"
        >
          {{ authStore.i18n("cm_find_inquireSummary") }} (<span>
            {{ cartData.stat?.selectSkuTotalQuantity }}</span
          >
          {{ authStore.i18n("cm_find_ items") }})
        </div>
      </template>
      <div class="mb-[0.48rem]">
        <div
          class="mb-[0.16rem] flex justify-between text-[0.28rem] font-medium"
        >
          <span class="text-gray-500">{{
            authStore.i18n("cm_find_itemsCost")
          }}</span
          ><span class="text-[#2D2D2D]">{{
            setUnit(cartData.stat.selectTotalSalePrice)
          }}</span>
        </div>
        <div
          class="bg-[rgba(247,186,42,.1)] mt-[0.48rem] px-[0.16rem] py-[0.08rem] flex text-[0.26rem]"
        >
          <icon-card
            size="0.4rem"
            name="mingcute:warning-line"
            color="#F7BA2A"
            class="mr-[0.08rem]"
          ></icon-card>
          <span>
            {{ authStore.i18n("cm_find_submitTip") }}
          </span>
        </div>
      </div>
    </n-drawer-content></n-drawer
  >
  <!-- 2000美元提交校验提示 -->
  <n-modal :show="pageData.submitDialogVisible" :show-icon="false">
    <n-card
      :bordered="false"
      style="
        width: 7.1rem;
        color: #000;
        padding: 0 0.2rem !important;
        text-align: center;
      "
    >
      <div class="text-[0.3rem] leading-[0.42rem] mb-[0.4rem] px-[0.02rem]">
        El importe de su producto es inferior a US$ 2000,
        <span class="text-[#e50113]"
          >los gastos de envío pueden ser más caros que el coste del
          producto</span
        >, se recomienda aumentar la compra a US$ 2000.
      </div>
      <div class="flex justify-between">
        <n-button
          round
          color="#fff"
          text-color="#000"
          data-spm-box="cart-to-checkout"
          @click="onConfirmSubmit($event, 'dialog')"
          class="border-btn w-[3.12rem] h-[0.68rem] p-0 text-[0.28rem]"
        >
          {{ authStore.i18n("cm_common.buySubmit") }}</n-button
        >
        <n-button
          round
          color="#E50113"
          text-color="#fff"
          class="w-[3.12rem] h-[0.68rem] p-0 text-[0.28rem]"
          @click="onCancelSubmit"
        >
          {{ authStore.i18n("cm_common.buyAgain") }}</n-button
        >
      </div>
    </n-card>
  </n-modal>
  <!-- 错误弹窗 -->
  <ErrorModal
    :message="pageData.errorMessage"
    v-model:visible="pageData.errorDialogVisible"
  />
  <!-- 生成订单loading -->
  <SubmitLoadingModal :show="pageData.submitLoading" />
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import MobileGoodsCard from "./MobileGoodsCard.vue";
import MobileSkuCard from "./MobileSkuCard.vue";
import ErrorModal from "./ErrorModal.vue";
import SubmitLoadingModal from "@/pages/find/components/SubmitLoadingModal.vue";

const authStore = useAuthStore();

const props = defineProps({
  cartData: {
    type: Object,
    default: () => {},
  },
  showTabs: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "onAllSelection",
  "onGoodsSelection",
  "onSkuSelection",
  "onCartQtyUpdate",
  "onDeleteGoods",
  "onDeleteSku",
  "onOpenSkuDialog",
  "onInquiry",
]);

const pageData = reactive<any>({
  errorMessage: "",
  showTipDrawer: false,
  errorDialogVisible: false,
  submitDialogVisible: false,
  submitLoading: false,
});

// 计算属性
const isSelectAll = computed(
  () =>
    props.cartData?.goodsList?.every((goods: any) => goods.selected) || false
);

// 方法
const formatPrice = (price: number) => {
  return `US$ ${price.toFixed(2)}`;
};

const onAllSelection = (value: boolean) => {
  emit("onAllSelection", value, false);
};

const onInquiry = () => {
  emit("onInquiry");
};

const onSaveCountry = () => {
  window.location.reload();
};

function openInquireTip() {
  pageData.showTipDrawer = !pageData.showTipDrawer;
}

function onGoFindSubmit(event: any) {
  (window as any)?.MyStat?.addPageEvent(
    "click_start_looking",
    `点击创建询盘按钮`
  ); // 埋点
  const currentStat = props.cartData?.stat;
  if (currentStat?.selectTotalSalePrice < 2000) {
    pageData.submitDialogVisible = true;
    (window as any)?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_open",
      `未满2000美元对话框-打开`
    ); // 埋点
    return;
  }
  onConfirmSubmit(event);
}

async function onConfirmSubmit(event: any, from?: any) {
  pageData.submitLoading = true;
  if (from) {
    (window as any)?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_ignore",
      `未满2000美元对话框-忽略`
    ); // 埋点
  }
  (window as any)?.MyStat?.addPageEvent(
    "carrito_click_inquiry",
    `购物车列表进入询盘提交页`
  ); // 埋点

  const selectedSkuList = <any>[];
  const currentGoodsList = props.cartData?.goodsList || [];
  currentGoodsList.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });

  if (!!(window as any)?.fbq) {
    (window as any)?.fbq("track", "InitiateCheckout", {
      currency: "USD",
      num_items: selectedSkuList?.length,
      contents: selectedSkuList,
    });
  }
  if (!!(window as any)?.ttq) {
    (window as any)?.ttq?.track("InitiateCheckout", {
      currency: "USD",
      value: props.cartData?.stat?.selectTotalSalePrice,
      content_type: "product",
      description: JSON.stringify(selectedSkuList),
    });
  }

  const res: any = await useGetInquiry({
    params: selectedSkuList,
    siteId: window.siteData.siteInfo.id,
  });
  if (res?.result?.code === 200) {
    const inquiryInfo = res?.data;
    await authStore.setInquiryInfo(inquiryInfo);
    navigateToPage(`/find/submit`, {}, false, event);
  } else if (res?.result?.code === 403) {
    emit("onGoLoginRegister");
  } else {
    window?.MyStat?.addPageEvent(
      "submit_start_looking_error",
      `进入询盘信息页错误：${res?.result?.message}`
    ); // 埋点
    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

function onCancelSubmit() {
  window?.MyStat?.addPageEvent(
    "less_then_2000usd_dialog_close",
    `未满2000美元对话框-关闭`
  ); // 埋点
  pageData.submitDialogVisible = false;
}
</script>

<style scoped lang="scss">
:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
  border-color: #e50113;
}

.border-btn {
  border: 0.02rem solid #c7c7c7;
  &:hover {
    background: #e50113;
    color: #fff;
    border: none;
  }
}

:deep(.n-card__content) {
  color: #000;
  padding: 0.48rem 0 !important;
  text-align: center;
}
</style>
