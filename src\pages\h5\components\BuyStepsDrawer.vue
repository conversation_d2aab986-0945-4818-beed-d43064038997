<template>
  <!-- 福利明细抽屉 -->
  <n-drawer
    v-model:show="pageData.dialogVisible"
    width="100%"
    placement="bottom"
    :trap-focus="false"
    default-height="88%"
  >
    <n-drawer-content closable>
      <template #header>
        <div
          class="w-[212px] mx-auto text-[20px] leading-[22px] text-center font-normal"
        >
          {{ authStore.i18n("cm_home_3stepPurchasing") }}
        </div>
      </template>

      <!-- 福利明细内容 -->
      <div
        class="benefit-content relative pt-[14px] pb-[30px] px-[20px] overflow-hidden"
      >
        <img
          alt="bg"
          loading="lazy"
          class="absolute top-[102px] right-[-83px] w-[280px]"
          src="@/assets/icons/home/<USER>"
        />
        <div
          v-for="(item, index) in props.benefitData"
          :key="index"
          class="benefit-item flex items-start gap-[16px] mb-[28px] last:mb-0"
        >
          <!-- 图标 -->
          <div
            class="w-[36px] h-[36px] bg-[#8C111B] rounded-full flex-shrink-0 flex items-center justify-center"
          >
            <img
              :src="item.icon"
              :alt="item.title"
              loading="lazy"
              class="w-[21px] h-[21px]"
            />
          </div>

          <!-- 内容 -->
          <div class="flex-1">
            <h3 class="text-[18px] leading-[18px] text-[#333] mb-[8px]">
              {{ item.title }}
            </h3>
            <p class="text-[16px] leading-[20px] text-[#7F7F7F]">
              {{ item.desc }}
            </p>
          </div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const props = defineProps({
  benefitData: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive({
  dialogVisible: false,
});

const openDrawer = () => {
  pageData.dialogVisible = true;
};

const closeDrawer = () => {
  pageData.dialogVisible = false;
};

defineExpose({
  openDrawer,
  closeDrawer,
});
</script>

<style lang="scss" scoped>
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
}
:deep(.n-drawer-header) {
  padding: 17px 18px !important;
}
:deep(.n-base-icon svg) {
  width: 16px !important;
  height: 16px !important;
}
:deep(.n-base-close) {
  color: #333 !important;
}
</style>
