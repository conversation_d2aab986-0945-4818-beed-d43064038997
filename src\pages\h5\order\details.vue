<template>
  <div class="bg-[#F2F2F2] min-h-[100vh] pt-[1rem] pb-[1.5rem] text-[0.28rem]">
    <div
      class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] items-center justify-center bg-white fixed top-0 z-10"
    >
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_order.orderDetailTitle") }}
      </div>
    </div>
    <div class="order-state">{{ pageData.statusDesc }}</div>
    <div class="page-content px-[0.16rem] pb-[1rem]">
      <!-- 地址信息 -->
      <div class="order-card flex items-center">
        <icon-card
          name="mdi:address-marker"
          size="26"
          color="#e50113"
          class="mr-[0.16rem]"
        >
        </icon-card>
        <div>
          <div class="mb-[0.08rem]">
            <span class="font-medium mr-[0.16rem]">{{
              pageData?.addressInfo?.userName
            }}</span>
            <span>{{ pageData?.addressInfo?.phone }}</span>
          </div>
          <div>{{ pageData?.addressInfo?.address }}</div>
        </div>
      </div>
      <!-- 物流信息 开单一期不做 暂时隐藏 -->
      <!-- <div class="order-card flex justify-between items-center">
        <div class="text-[0.32rem] font-medium text-[#333]">物流信息</div>
        <div class="text-blue cursor-pointer" @click="onOpenLogDetail">
          查看详情
        </div>
      </div> -->
      <!-- 运输报价 -->
      <div class="order-card" v-if="pageData.transportAmountList?.length > 0">
        <div
          class="flex justify-between pb-[0.1rem] text-[0.32rem] font-medium"
          v-if="
            pageData?.sourcePlatform === 'ORDER_SOURCE_PLATFORM_ONLINE_MALL'
          "
        >
          <span class="text-[#333]">{{
            authStore.i18n("cm_order.shippingOption")
          }}</span>
        </div>
        <div
          v-else
          class="flex justify-between pb-[0.1rem] text-[0.32rem] font-medium"
        >
          <span class="text-[#333]">{{
            authStore.i18n("cm_order.chooseTransport")
          }}</span>
          <icon-card
            name="material-symbols:help"
            size="22"
            color="#e50113"
            @click="onOpenLoadTrans"
            class="ml-[0.2rem]"
          >
          </icon-card>
        </div>
        <n-radio-group
          class="flex flex-wrap justify-between"
          v-model:value="pageData.transportId"
          :on-update:value="onUpdateTrans"
          :disabled="!isPayFee"
        >
          <n-collapse
            :show-arrow="false"
            display-directive="show"
            :trigger-areas="['extra']"
            :default-expanded-names="pageData.expandedTransport"
          >
            <div
              class="transport-box"
              :class="
                pageData.transportId === transport.transportId
                  ? '!border-[#e50113]'
                  : ''
              "
              v-for="transport in pageData.transportAmountList"
              :key="transport.transportId"
            >
              <n-collapse-item :name="transport.transportId">
                <template #header>
                  <n-radio
                    :value="transport.transportId"
                    class="font-[0.36rem]"
                    >{{ transport.name }}</n-radio
                  >
                </template>
                <template #header-extra="{ collapsed }">
                  <icon-card
                    v-if="collapsed"
                    name="uil:angle-down"
                    color="#797979"
                    size="32"
                  ></icon-card>
                  <icon-card
                    v-else
                    name="uil:angle-up"
                    color="#797979"
                    size="32"
                  ></icon-card>
                </template>
                <template #arrow><span class="hidden"></span></template>
                <n-space vertical :style="{ gap: '0.28rem 0' }">
                  <n-space vertical :style="{ gap: '0.16rem 0' }">
                    <div class="flex justify-between">
                      <span>{{ authStore.i18n("cm_order.feeAmount") }}</span>
                      <div class="flex flex-col items-end">
                        <span class="font-medium">{{
                          setUnit(transport.amount)
                        }}</span>
                        <n-button
                          size="small"
                          color="#F6D2D4"
                          text-color="#E50013"
                          class="rounded-[0.08rem] h-[0.4rem]"
                          v-if="transport?.amountDetailList?.length > 0"
                        >
                          <span
                            class="text-[0.24rem] leading-[0.4rem]"
                            @click="
                              onShowFeeDetails(transport?.amountDetailList)
                            "
                            >{{
                              authStore.i18n("cm_order.feeAmountDetails")
                            }}</span
                          >
                        </n-button>
                      </div>
                    </div>
                    <div class="flex justify-between">
                      <span>{{
                        authStore.i18n("cm_order.expectDeliveryTime")
                      }}</span
                      ><span class="font-medium">{{
                        transport.expectDeliveryTime
                      }}</span>
                    </div>
                  </n-space>
                  <div class="text-[#868686]" v-if="transport.transportRemark">
                    <div class="mb-[0.06rem]">
                      {{ authStore.i18n("cm_order.shippingInformation") }}
                    </div>
                    <div>{{ transport.transportRemark }}</div>
                  </div>
                </n-space>
              </n-collapse-item>
            </div>
          </n-collapse>
        </n-radio-group>
      </div>
      <!-- 商品信息 -->
      <div class="order-card text-[0.26rem] !px-[0.08rem]">
        <div class="mb-[0.1rem]">
          <span class="text-[#666] inline-block min-w-[2.52rem] mr-[0.1rem]">{{
            authStore.i18n("cm_order.orderNo")
          }}</span
          ><span>{{ pageData.orderNo }}</span>
        </div>
        <div class="mb-[0.3rem]">
          <span class="text-[#666] inline-block min-w-[2.52rem] mr-[0.1rem]">{{
            authStore.i18n("cm_order.orderTime")
          }}</span
          ><span>{{ timeFormatByZone(pageData.orderTime) }}</span>
        </div>
        <n-space vertical :style="{ gap: '0.24rem 0' }">
          <div
            v-for="(box, boxIndex) in pageData.boxList"
            :key="box.boxId"
            class="border-l-[0.08rem] rounded-[0.04rem] border-[#FF6B81] pl-[0.16rem]"
          >
            <n-space vertical :style="{ gap: '0.24rem 0' }">
              <div
                v-for="(sku, index) in box.skuList"
                :key="index"
                class="flex text-[0.26rem] text-[#000]"
              >
                <n-image
                  lazy
                  :src="sku.picUrl"
                  class="w-[1.24rem] h-[1.24rem] flex-shrink-0 mr-[0.2rem]"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
                <div class="flex-1">
                  <a
                    :href="`/h5/goods/${sku.goodsId}${
                      sku.padc ? `?padc=${sku.padc}` : ''
                    }`"
                    class="goodsName-ellipsis"
                    :data-spm-index="index + 1"
                    data-spm-box="order-detail-goods"
                  >
                    {{ sku.goodsName }}
                  </a>
                  <div class="text-[0.24rem] text-[#7F7F7F] leading-[0.34rem]">
                    {{ getSkuName(sku.specList) }}
                  </div>
                  <div class="text-[0.24rem] text-[#7F7F7F]">
                    {{ authStore.i18n("cm_order.skuNo") }}：{{ sku.skuNo }}
                  </div>
                  <div class="flex justify-between">
                    <div>
                      x {{ sku.count
                      }}<span class="ml-[0.08rem]">{{
                        sku.goodsPriceUnitName
                      }}</span>
                    </div>
                    <span>{{ setUnit(sku.unitPrice) }}</span>
                  </div>
                  <!-- 到手单价 -->
                  <div
                    class="flex justify-between"
                    v-if="
                      !isEmptyObject(pageData.currentTransport) &&
                      pageData.transportAmountType === 1
                    "
                  >
                    <div class="flex items-center">
                      <div class="text-[#e50113] mr-[0.12rem]">
                        {{ authStore.i18n("cm_order.receivedUnitPrice") }}
                      </div>
                      <n-popover trigger="hover">
                        <template #trigger>
                          <icon-card
                            name="mingcute:question-fill"
                            size="15"
                            color="#e50113"
                          >
                          </icon-card>
                        </template>
                        <div class="w-[4rem]">
                          {{ authStore.i18n("cm_order.receivedUnitPriceTip") }}
                        </div>
                      </n-popover>
                    </div>
                    <div class="text-[#E50113]">
                      {{ setUnit(sku.receivedUnitPrice) }}
                    </div>
                  </div>
                </div>
              </div>
            </n-space>
            <div class="mt-[0.16rem] bg-[#FAFAFA] px-[0.24rem] py-[0.12rem]">
              <div class="flex justify-between">
                <span>{{ authStore.i18n("cm_order.boxCount") }}</span>
                <span>{{ box.boxCount }}</span>
              </div>
              <div
                class="flex justify-between mt-[0.08rem] pt-[0.08rem] border-t-1 border-[#CCC] border-dashed"
              >
                <span>{{ authStore.i18n("cm_order.boxSkuCount") }}</span>
                <span>{{ box.skuCount }}</span>
              </div>
              <!-- 箱运费 -->
              <div
                v-if="
                  !isEmptyObject(pageData.currentTransport) &&
                  pageData.transportAmountType === 1
                "
                class="flex justify-between mt-[0.08rem] pt-[0.08rem] border-t-1 border-[#CCC] border-dashed"
              >
                <span>{{ authStore.i18n("cm_order.boxAmount") }}</span>
                <span>{{ setUnit(box.boxFee) }}</span>
              </div>
            </div>
          </div>
        </n-space>
      </div>
      <!-- 订单费用明细 -->
      <div class="order-card !px-0 text-[0.26rem]">
        <div
          class="text-[0.32rem] leading-[0.32rem] font-medium px-[0.16rem] mb-[0.4rem]"
        >
          {{ authStore.i18n("cm_order.orderFeeDetails") }}
        </div>
        <!-- 后台返回的费用展示 -->
        <div>
          <div
            class="px-[0.16rem] flex justify-between border-b border-[#d7d7d7] pb-[0.16rem] mb-[0.1rem] text-[0.28rem] leading-[0.28rem] font-medium"
          >
            <span class="text-[#333]">{{
              authStore.i18n("cm_order.productCost")
            }}</span>
            <span v-if="pageData.productAmount?.amount"
              >{{ setUnit(pageData.productAmount?.amount) }}
            </span>
          </div>
          <div
            class="px-[0.16rem] flex justify-between border-[#A9A9A9] my-[0.06rem]"
            v-for="(fee, feeIndex) in pageData.productAmount.feeList"
            :key="feeIndex"
          >
            <span class="text-[#333] w-[60%]">{{ fee.feeName }}:</span>
            <span class="flex items-center">
              <n-button
                size="small"
                color="#F6D2D4"
                text-color="#E50013"
                class="rounded-[0.08rem] mr-[0.16rem] h-[0.4rem]"
                v-if="fee.childFeeList?.length > 0"
              >
                <span
                  class="text-[0.24rem] leading-[0.4rem]"
                  @click="onShowFeeDetails(fee.childFeeList)"
                  >{{ authStore.i18n("cm_order.feeAmountDetails") }}</span
                >
              </n-button>
              <span>{{ setUnit(fee.feeAmount) }}</span>
            </span>
          </div>
        </div>
        <!-- 国际费用 用户选择线路会展示相应的费用 -->
        <div
          class="mt-[0.32rem]"
          v-if="!isEmptyObject(pageData.currentTransport)"
        >
          <div
            class="px-[0.16rem] flex justify-between border-b border-[#d7d7d7] pb-[0.16rem] mb-[0.1rem] text-[0.28rem] leading-[0.28rem] font-medium"
          >
            <span class="text-[#333]"
              >{{ authStore.i18n("cm_order.interFees") }}：</span
            >
            <span v-if="pageData.currentTransport?.amount"
              >{{ setUnit(pageData.currentTransport?.amount) }}
            </span>
          </div>
          <div class="border-[#A9A9A9]">
            <div class="px-[0.16rem] flex justify-between my-[0.16rem]">
              <span>{{ authStore.i18n("cm_order.feeAmount") }}</span>
              <div class="flex flex-col items-end">
                <span>{{ setUnit(pageData.currentTransport?.amount) }}</span>
                <n-button
                  size="small"
                  color="#F6D2D4"
                  text-color="#E50013"
                  class="rounded-[0.08rem] h-[0.4rem]"
                  v-if="pageData.currentTransport?.amountDetailList?.length > 0"
                >
                  <span
                    class="text-[0.24rem] leading-[0.4rem]"
                    @click="
                      onShowFeeDetails(
                        pageData.currentTransport?.amountDetailList
                      )
                    "
                    >{{ authStore.i18n("cm_order.feeAmountDetails") }}</span
                  >
                </n-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 账单费用明细 -->
      <div
        class="order-card !px-0 text-[0.26rem]"
        v-if="
          pageData.mallOrderStatus &&
          pageData.mallOrderStatus !== 'MALL_WAITING_APPROVING' &&
          !isOrderCanceled &&
          !isPayAllFee &&
          !isPayProduct
        "
      >
        <div
          class="text-[0.32rem] leading-[0.32rem] font-medium px-[0.16rem] mb-[0.4rem]"
        >
          {{ authStore.i18n("cm_order.paidFeeDetails") }}
        </div>
        <!-- 后台返回的费用展示 -->
        <div>
          <div
            class="px-[0.16rem] flex justify-between border-b border-[#d7d7d7] pb-[0.16rem] mb-[0.1rem] text-[0.28rem] leading-[0.28rem] font-medium"
          >
            <span class="text-[#333]">{{
              authStore.i18n("cm_order.productCost")
            }}</span>
            <span v-if="pageData.productAmount?.amount"
              >{{ setUnit(pageData.productAmount?.amount) }}
            </span>
          </div>
          <div
            class="px-[0.16rem] flex justify-between border-[#A9A9A9] my-[0.06rem]"
            v-for="(fee, feeIndex) in pageData.productAmount.feeList"
            :key="feeIndex"
          >
            <span class="text-[#333] w-[60%]">{{ fee.feeName }}:</span>
            <span class="flex items-center">
              <n-button
                size="small"
                color="#F6D2D4"
                text-color="#E50013"
                class="rounded-[0.08rem] mr-[0.16rem] h-[0.4rem]"
                v-if="fee.childFeeList?.length > 0"
              >
                <span
                  class="text-[0.24rem] leading-[0.4rem]"
                  @click="onShowFeeDetails(fee.childFeeList)"
                  >{{ authStore.i18n("cm_order.feeAmountDetails") }}</span
                >
              </n-button>
              <span>{{ setUnit(fee.feeAmount) }}</span>
            </span>
          </div>
        </div>
        <!-- 国际费用 用户选择线路会展示相应的费用 -->
        <div
          class="mt-[0.32rem]"
          v-if="
            !isEmptyObject(pageData.currentTransport) && !hideInterFeeDisplay
          "
        >
          <div
            class="px-[0.16rem] flex justify-between border-b border-[#d7d7d7] pb-[0.16rem] mb-[0.1rem] text-[0.28rem] leading-[0.28rem] font-medium"
          >
            <span class="text-[#333]"
              >{{ authStore.i18n("cm_order.interFees") }}：</span
            >
            <span v-if="pageData.currentTransport?.amount"
              >{{ setUnit(pageData.currentTransport?.amount) }}
            </span>
          </div>
          <div class="border-[#A9A9A9]">
            <div class="px-[0.16rem] flex justify-between my-[0.16rem]">
              <span>{{ authStore.i18n("cm_order.feeAmount") }}</span>
              <div class="flex flex-col items-end">
                <span>{{ setUnit(pageData.currentTransport?.amount) }}</span>
                <n-button
                  size="small"
                  color="#F6D2D4"
                  text-color="#E50013"
                  class="rounded-[0.08rem] h-[0.4rem]"
                  v-if="pageData.currentTransport?.amountDetailList?.length > 0"
                >
                  <span
                    class="text-[0.24rem] leading-[0.4rem]"
                    @click="
                      onShowFeeDetails(
                        pageData.currentTransport?.amountDetailList
                      )
                    "
                    >{{ authStore.i18n("cm_order.feeAmountDetails") }}</span
                  >
                </n-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 优惠券明细（支付成功后，用户优惠券明细展示） 线下支付不展示优惠券明细-->
        <div
          class="mt-[0.32rem]"
          v-if="pageData.productAddComissSumAmount && !isPayOffline"
        >
          <div
            class="px-[0.16rem] flex justify-between border-b border-[#d7d7d7] pb-[0.16rem] mb-[0.1rem] text-[0.28rem] leading-[0.28rem] font-medium"
          >
            <span class="text-[#333]"
              >{{ authStore.i18n("cm_order.coupon") }}:</span
            >
            <span>{{ setUnit(pageData.productAddComissSumAmount) }} </span>
          </div>
          <div class="border-[#A9A9A9]">
            <n-space vertical :style="{ gap: '0.16rem 0' }">
              <coupon-detail
                v-if="pageData.productCouponAmount.amount"
                :title="authStore.i18n('cm_coupon.productCoupons')"
                :amount="pageData.productCouponAmount.amount"
                couponType="COUPON_TYPE_PRODUCT"
                :couponList="pageData.productCouponAmount.productCouponList"
              ></coupon-detail>
              <coupon-detail
                v-if="pageData.comissCouponAmount.amount"
                :title="authStore.i18n('cm_coupon.commissionCoupons')"
                :amount="pageData.comissCouponAmount.amount"
                couponType="COUPON_TYPE_COMMISSION"
                :couponList="pageData.comissCouponAmount.commissionCouponList"
              ></coupon-detail>
            </n-space>
          </div>
        </div>
        <!-- 实付款 -->
        <div class="mt-[0.32rem]">
          <div
            class="px-[0.16rem] flex justify-between pb-[0.12rem] text-[0.32rem] leading-[0.32rem] font-medium"
          >
            <span class="text-[#333]"
              >{{ authStore.i18n("cm_order.actualPaymentTotal") }}:</span
            >
            <span class="text-[#E50113]"
              >{{ setUnit(pageData.paidAmount) }}
            </span>
          </div>
        </div>
      </div>
      <!-- 待付款信息 -->
      <div class="order-card px-[0.16rem]" v-if="isPayFee">
        <!-- 线下支付 || 支付国际运费 -->
        <template v-if="isPayOffline || isPayInterFee">
          <!-- 【支付产品成本，支付订单费用】展示数量 -->
          <div
            v-if="isPayAllFee || isPayProduct"
            class="flex justify-between items-center text-[0.28rem] leading-[0.28rem] pb-[0.24rem] mb-[0.24rem] border-b-1 border-[#F2F2F2]"
          >
            <span>{{ authStore.i18n("cm_order.productQuantity") }}</span>
            <span class="">
              {{ pageData.totalCount }}
            </span>
          </div>
          <div
            class="flex justify-between text-[0.32rem] leading-[0.32rem] mb-[0.04rem]"
          >
            <div class="font-medium w-[70%] flex-shrink-0">
              {{ authStore.i18n("cm_order.amountToPay") }}
            </div>
            <span class="font-medium text-[#e50113] break-all">
              {{ setUnit(pageData.actualPaymentAmount) }}
            </span>
          </div>
          <div
            class="text-[0.24rem] text-[#999] flex items-center"
            v-if="pageData.paymentAmountMessage"
          >
            <icon-card
              size="14"
              name="mingcute:warning-line"
              color="#999"
              class="mr-[0.02rem]"
            ></icon-card>
            {{ pageData.paymentAmountMessage.toLowerCase() }}
          </div>
        </template>
        <template v-else>
          <div class="flex justify-between items-center">
            <span>{{ authStore.i18n("cm_order.totalPaymentAmount") }}</span>
            <span v-if="pageData.paymentAmount" class="font-medium">
              {{ setUnit(pageData.paymentAmount) }}
            </span>
          </div>
          <div
            class="text-[0.24rem] text-[#999] flex items-center"
            v-if="pageData.paymentAmountMessage"
          >
            <icon-card
              size="14"
              name="mingcute:warning-line"
              color="#999"
              class="mr-[0.02rem]"
            ></icon-card>
            {{ pageData.paymentAmountMessage.toLowerCase() }}
          </div>
          <!-- 优惠券 -->
          <div class="mt-[0.28rem]">
            <div class="flex justify-between items-center">
              <span>{{ authStore.i18n("cm_order.coupon") }}:</span>
              <span class="text-[#E50013] font-medium">
                {{ setUnit(pageData.discountedAmount) }}
              </span>
            </div>
            <div>
              <!-- 产品券 -->
              <div class="my-[0.24rem]">
                <div
                  v-if="pageData.couponInfo?.productCouponList?.length"
                  class="flex justify-between items-center leading-[0.28rem]"
                >
                  <div class="text-[0.28rem]">
                    {{ authStore.i18n("cm_coupon.productCoupons") }}
                  </div>
                  <div
                    class="flex cursor-pointer text-[#E50113]"
                    @click="onChooseCoupon('COUPON_TYPE_PRODUCT')"
                  >
                    <div
                      v-if="pageData.couponInfo.productCouponAmount"
                      class="text-[0.28rem] font-medium"
                    >
                      {{ setUnit(pageData.couponInfo.productCouponAmount) }}
                    </div>
                    <div v-else class="text-[0.24rem] font-medium">
                      {{ authStore.i18n("cm_order.youHaveCoupon") }}
                      {{ pageData.couponInfo?.productCouponList?.length }}
                      {{ authStore.i18n("cm_order.availableCoupons") }}
                    </div>
                    <img
                      loading="lazy"
                      alt="arrow"
                      class="w-[0.16rem] ml-[0.04rem]"
                      src="@/assets/icons/leftArrow.svg"
                      referrerpolicy="no-referrer"
                    />
                  </div>
                </div>
                <div
                  v-else
                  class="flex justify-between items-center leading-[0.28rem]"
                >
                  <div class="text-[0.28rem]">
                    {{ authStore.i18n("cm_coupon.productCoupons") }}
                  </div>
                  <div class="flex">
                    <div class="text-[0.24rem] text-[#7F7F7F]">
                      {{ authStore.i18n("cm_order.noAvailableCoupons") }}
                    </div>
                    <img
                      loading="lazy"
                      alt="arrow"
                      class="w-[0.16rem] ml-[0.04rem]"
                      src="@/assets/icons/leftArrow.svg"
                      referrerpolicy="no-referrer"
                    />
                  </div>
                </div>
              </div>
              <!-- 佣金券 -->
              <div class="my-[0.24rem]">
                <div
                  v-if="pageData.couponInfo?.commissionCouponList?.length"
                  class="flex justify-between items-center leading-[0.28rem]"
                >
                  <div class="text-[0.28rem]">
                    {{ authStore.i18n("cm_coupon.commissionCoupons") }}
                  </div>
                  <div
                    class="flex cursor-pointer text-[#E50113]"
                    @click="onChooseCoupon('COUPON_TYPE_COMMISSION')"
                  >
                    <div
                      v-if="pageData.couponInfo.commissionCouponAmount"
                      class="text-[0.28rem] font-medium"
                    >
                      {{ setUnit(pageData.couponInfo.commissionCouponAmount) }}
                    </div>
                    <div v-else class="text-[0.24rem] font-medium">
                      {{ authStore.i18n("cm_order.youHaveCoupon") }}
                      {{ pageData.couponInfo?.commissionCouponList?.length }}
                      {{ authStore.i18n("cm_order.availableCoupons") }}
                    </div>
                    <img
                      loading="lazy"
                      alt="arrow"
                      class="w-[0.16rem] ml-[0.04rem]"
                      src="@/assets/icons/leftArrow.svg"
                      referrerpolicy="no-referrer"
                    />
                  </div>
                </div>
                <div
                  v-else
                  class="flex justify-between items-center leading-[0.28rem]"
                >
                  <div class="text-[0.28rem]">
                    {{ authStore.i18n("cm_coupon.commissionCoupons") }}
                  </div>
                  <div class="flex">
                    <div class="text-[0.24rem] text-[#7F7F7F]">
                      {{ authStore.i18n("cm_order.noAvailableCoupons") }}
                    </div>
                    <img
                      loading="lazy"
                      alt="arrow"
                      class="w-[0.16rem] ml-[0.04rem]"
                      src="@/assets/icons/leftArrow.svg"
                      referrerpolicy="no-referrer"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 产品数量 -->
          <div
            class="flex justify-between items-center text-[0.28rem] leading-[0.28rem] py-[0.28rem] my-[0.28rem] border-t-1 border-b-1 border-[#F2F2F2]"
          >
            <span>{{ authStore.i18n("cm_order.productQuantity") }}</span>
            <span class="">
              {{ pageData.totalCount }}
            </span>
          </div>
          <div
            class="flex justify-between items-center text-[0.32rem] leading-[0.32rem]"
          >
            <span class="font-medium">
              {{ authStore.i18n("cm_order.amountToPay") }}
            </span>
            <span class="font-medium text-[#e50113]">
              {{ setUnit(pageData.actualPaymentAmount) }}
            </span>
          </div>
        </template>
      </div>
      <!-- 商家备注 -->
      <div class="order-card" v-if="pageData.merChantRemark">
        <div class="text-[0.32rem] font-medium text-[#333] pb-[0.16rem]">
          {{ authStore.i18n("cm_order.merchantRemark") }}
        </div>
        <n-scrollbar
          class="border border-[#d7d7d7] py-[0.12rem] px-[0.16rem] rounded-[0.04rem] min-h-[1.6rem] max-h-[3rem] cursor-not-allowed"
        >
          <span class="whitespace-pre-line">{{ pageData.merChantRemark }}</span>
        </n-scrollbar>
      </div>

      <!-- 客户备注 -->
      <div class="order-card">
        <div class="text-[0.32rem] font-medium text-[#333] pb-[0.16rem]">
          {{ authStore.i18n("cm_order.customerRemark") }}
        </div>
        <n-input
          :autosize="{
            minRows: 3,
            maxRows: 6,
          }"
          clearable
          v-if="isPayFee"
          maxlength="1000"
          type="textarea"
          v-model:value="pageData.orderRemark"
          :on-blur="onOrderRemarkTrack"
          :placeholder="authStore.i18n('cm_order.orderRemarkInput')"
        />
        <n-scrollbar
          v-else
          class="border border-[#d7d7d7] py-[0.12rem] px-[0.16rem] rounded-[0.04rem] min-h-[1.6rem] max-h-[3rem] cursor-not-allowed"
        >
          <span>{{ pageData.orderRemark }}</span>
        </n-scrollbar>
      </div>
    </div>
    <div class="page-footer" v-if="isPayFee">
      <div class="w-full flex justify-between items-center">
        <div class="font-medium text-[0.32rem]">
          <span v-if="pageData.actualPaymentAmount">
            {{ setUnit(pageData.actualPaymentAmount) }}
          </span>
        </div>
        <div>
          <div class="flex">
            <n-button
              color="#F6D2D4"
              text-color="#E50013"
              @click="onOrderCancel"
              :disabled="!!pageData.preview"
              class="rounded-[0.4rem] w-[1.64rem] h-[0.6rem] text-[0.26rem] mr-[0.1rem]"
            >
              {{ authStore.i18n("cm_order.orderCancel") }}
            </n-button>
            <n-button
              :color="isPayOffline ? '#E6E6E6' : '#E50113'"
              :text-color="isPayOffline ? '#000' : '#fff'"
              :disabled="isPayOffline || !pageData.supportOnlinePay"
              class="rounded-[0.4rem] w-[1.44rem] h-[0.6rem] text-[0.26rem]"
              data-spm-box="order-detail-go-pay"
              @click="onOrderPay"
            >
              {{ authStore.i18n("cm_order.orderPay") }}
            </n-button>
          </div>
        </div>
      </div>
      <div
        @click="onWhatsAppClick"
        class="mt-[0.02rem] text-[0.24rem] text-[#e50113]"
        v-if="isPayOffline || !pageData.supportOnlinePay"
      >
        <icon-card
          size="18"
          name="mingcute:warning-line"
          color="#e50113"
          class="mr-[0.06rem]"
        ></icon-card
        >{{ authStore.i18n("cm_order.orderPayTip") }}
        <img
          alt="whatsapp"
          class="inline-block w-[0.36rem] ml-[0.08rem]"
          src="@/assets/icons/common/whatsapp.svg"
        />
      </div>
      <div class="flex text-[0.28rem] leading-[0.3rem] mt-[0.2rem]">
        <n-checkbox
          size="medium"
          class="agree-checkbox"
          v-model:checked="pageData.acceptTerms"
          :on-update:checked="onUpdateAcceptTerms"
        >
        </n-checkbox>
        <div class="ml-[0.12rem]">
          <span class="text-[#666] mr-[0.08rem]">{{
            authStore.i18n("cm_order.readAgree")
          }}</span>
          <span
            @click="onOpenAgreeModal"
            class="font-medium text-[#206CCF] hover:underline cursor-pointer"
            >{{ authStore.i18n("cm_news.termsOfService") }}
          </span>
        </div>
      </div>
    </div>
    <!-- 物流详情 -->
    <!-- <n-modal
      preset="dialog"
      :block-scroll="true"
      :show-icon="false"
      v-model:show="pageData.showLogDetails"
      title="物流详情"
    >
      <div class="flex flex-col items-center mt-[0.4rem]">
        <n-timeline>
          <n-timeline-item
            color="#e50113"
            title="重新派送"
            content="包裹正在等待重新派送"
            time="2024-08-23 16:00"
            line-type="dashed"
          >
            <template #icon>
              <icon-card
                name="lets-icons:check-fill"
                size="28"
                color="#E50113"
                class="z-10"
              >
              </icon-card>
            </template>
          </n-timeline-item>
          <n-timeline-item
            content="物流单创建"
            time="2024-08-23 10:00"
            line-type="dashed"
          />
          <n-timeline-item
            title="已发货"
            content="包裹已成功发出"
            time="2024-08-23 12:30"
            line-type="dashed"
          />
          <n-timeline-item
            title="派送失败"
            content="因地址问题，派送失败"
            time="2024-08-23 15:00"
            line-type="dashed"
          />

          <n-timeline-item
            title="已完成"
            content="包裹已成功送达"
            time="2024-08-23 18:00"
            line-type="dashed"
          />
          <n-timeline-item content="感谢使用我们的服务" />
        </n-timeline>
      </div>
    </n-modal> -->

    <!-- 费用明细 -->
    <n-modal
      preset="dialog"
      :block-scroll="true"
      :show-icon="false"
      v-model:show="pageData.showComDetails"
      :title="authStore.i18n('cm_order.feeAmountDetails')"
    >
      <div class="flex flex-col items-center mt-[0.5rem]">
        <n-space
          vertical
          :style="{ gap: '0.16rem 0' }"
          class="w-full px-[0.2rem]"
        >
          <div
            :key="index"
            class="flex justify-between"
            v-for="(fee, index) in pageData.feeDetailsList"
          >
            <span class="text-[#666]">{{ fee.feeName }}：</span>
            <span>{{ setUnit(fee.feeAmount) }}</span>
          </div>
        </n-space>

        <n-button
          color="#E50113"
          text-color="#fff"
          @click="pageData.showComDetails = false"
          class="rounded-[0.16rem] w-[2.52rem] h-[0.7rem] text-[0.32rem] mt-[0.5rem]"
        >
          {{ authStore.i18n("cm_order.transKnown") }}
        </n-button>
      </div>
    </n-modal>

    <!-- 为什么估计装运 -->
    <n-modal
      preset="dialog"
      :block-scroll="true"
      :show-icon="false"
      v-model:show="pageData.showLoadTrans"
      :title="authStore.i18n('cm_order.WETrans')"
    >
      <div class="flex flex-col items-center mt-[0.3rem]">
        <n-space vertical :style="{ gap: '0.28rem 0' }">
          <div>
            {{ authStore.i18n("cm_order.transQuotation") }}
          </div>
          <div>
            {{ authStore.i18n("cm_order.betterTrans") }}
          </div>
          <div>
            {{ authStore.i18n("cm_order.transService") }}
          </div>
        </n-space>

        <n-button
          color="#E50113"
          text-color="#fff"
          @click="pageData.showLoadTrans = false"
          class="rounded-[0.16rem] w-[2.52rem] h-[0.7rem] text-[0.32rem] mt-[0.32rem]"
        >
          {{ authStore.i18n("cm_order.transKnown") }}
        </n-button>
      </div>
    </n-modal>

    <!-- 底部付款信息展示 -->
    <!-- <n-drawer
      v-model:show="pageData.showAmountDetails"
      width="100%"
      placement="bottom"
      :trap-focus="false"
      default-height="3.6rem"
    >
      <n-drawer-content
        closable
        :title="authStore.i18n('cm_order.payInformation')"
      >
        <div class="text-[#222] text-[0.28rem]">
          <div class="flex justify-between mb-[0.16rem]">
            <span>{{ authStore.i18n("cm_order.productQuantity") }}</span>
            <span class="text-[0.28rem] font-medium">{{
              pageData.totalCount
            }}</span>
          </div>
          <div class="flex justify-between">
            <span>{{ authStore.i18n("cm_order.paymentAmount") }}</span>
            <span class="font-medium text-[0.32rem]"
              >{{ setUnit(pageData.paymentAmount) }}
            </span>
          </div>
          <div
            class="text-[0.26rem] text-[#e50113]"
            v-if="pageData.paymentAmountMessage"
          >
            ({{ pageData.paymentAmountMessage }})
          </div>
        </div>
      </n-drawer-content>
    </n-drawer> -->

    <!-- 点击付款 付款信息错误提示 -->
    <n-modal
      preset="dialog"
      :block-scroll="true"
      :show-icon="false"
      v-model:show="pageData.showPayInfoError"
      :style="{
        padding: '0.4rem 0.8rem',
      }"
      :on-close="onClosePayError"
      :on-esc="onClosePayError"
      :on-mask-click="onClosePayError"
    >
      <div class="text-center">
        <icon-card
          size="22"
          name="mingcute:warning-line"
          color="#e50113"
          class="mr-[0.04rem]"
        ></icon-card>
        <span class="text-[0.28rem]">
          {{ authStore.i18n("cm_order.refreshPage") }}
        </span>
      </div>
    </n-modal>

    <!-- 点击付款 优惠券信息错误提示 -->
    <n-modal
      preset="dialog"
      :show-icon="false"
      :block-scroll="true"
      v-model:show="pageData.showCouponInfoError"
      :style="{
        padding: '0.4rem 0.8rem',
      }"
      :closable="false"
      :closeOnEsc="false"
      :maskClosable="false"
    >
      <div class="text-center">
        <div>
          <icon-card
            size="18"
            color="#e50113"
            class="mr-[0.04rem]"
            name="mingcute:warning-line"
          ></icon-card>
          <span class="text-[0.28rem]">
            {{ authStore.i18n("cm_order.chooseCouponError") }}
          </span>
        </div>

        <n-button
          color="#E50113"
          text-color="#fff"
          @click="onChooseCouponAgain"
          class="rounded-[0.3rem] h-[0.6rem] text-[0.28rem] mt-[0.2rem]"
        >
          {{ authStore.i18n("cm_order.chooseCouponAgain") }}
        </n-button>
      </div>
    </n-modal>

    <!-- 选择优惠券 -->
    <n-drawer
      width="100%"
      placement="bottom"
      :trap-focus="false"
      default-height="80%"
      v-model:show="pageData.showCouponChoose"
      :on-esc="onChooseCouponCancel"
      :on-mask-click="onChooseCouponCancel"
    >
      <div class="h-full flex flex-col py-0">
        <div class="relative pt-[0.28rem] pb-[0.48rem]">
          <div class="text-[0.36rem] leading-[0.36rem] font-medium text-center">
            {{
              pageData.chooseCouponType === "COUPON_TYPE_PRODUCT"
                ? authStore.i18n("cm_coupon.productCoupons")
                : authStore.i18n("cm_coupon.commissionCoupons")
            }}
          </div>
          <icon-card
            color="#222"
            name="iconamoon:close-light"
            @click="onChooseCouponCancel"
            class="w-[0.48rem] absolute right-[0.26rem] top-[0.26rem]"
          ></icon-card>
        </div>
        <n-scrollbar class="flex-1 px-[0.16rem]" trigger="none">
          <div v-show="pageData.chooseCouponLoading" class="loading-overlay">
            <n-spin stroke="#e50113" :show="pageData.chooseCouponLoading">
            </n-spin>
          </div>
          <n-checkbox-group
            v-model:value="pageData.selectedCouponIds"
            @update:value="onCheckAvailableList"
            class="flex flex-col items-center w-full coupon-wrapper"
          >
            <n-checkbox
              class="w-full coupon-card flex items-center pb-[0.2rem]"
              v-for="(coupon, index) in pageData.chooseCouponList"
              :key="coupon.id"
              :class="{
                'mb-[0.2rem] border-b border-[#F2F2F2]':
                  index !== pageData.chooseCouponList.length - 1,
                'checked-coupon': coupon?.check,
                'disabled-coupon': coupon?.availableFlag === false,
              }"
              :value="coupon.id"
              :disabled="
                coupon?.availableFlag === false ||
                coupon?.ticketStatus === 'TICKET_LOCK'
              "
            >
              <div class="flex-1 flex items-center">
                <div
                  class="coupon-border py-[0.28rem] border-[0.04rem] border-dotted w-[1.56rem] text-center text-[0.28rem] leading-[0.28rem] font-medium rounded-[0.08rem]"
                >
                  <span v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
                    {{ discountToPercentage(coupon.discount) }}
                    {{ authStore.i18n("cm_coupon.discount").toLowerCase() }}
                  </span>
                  <span v-else>
                    {{ setNewUnit(coupon?.preferentialAmount) }}
                  </span>
                </div>
                <div class="flex-1 mx-[0.14rem]">
                  <div class="text-[0.26rem] leading-[0.26rem] font-medium">
                    <!-- 满多少金额使用 -->
                    <template v-if="coupon?.couponUseConditionsType === 'FULL'">
                      <span>
                        {{ authStore.i18n("cm_coupon.minimumRequired") }}
                      </span>
                      {{ setNewUnit(coupon?.useConditionsAmount) }}
                    </template>
                    <!-- 每满多少金额使用 -->
                    <template
                      v-if="coupon?.couponUseConditionsType === 'EVERY_FULL'"
                    >
                      <span>
                        {{ authStore.i18n("cm_coupon.minimumUnmet") }}
                      </span>
                      {{ setNewUnit(coupon?.useConditionsAmount) }}
                      <span>
                        {{ authStore.i18n("cm_coupon.minimumUnmetCost") }}
                      </span>
                    </template>
                    <!-- 不限制金额 -->
                    <div v-if="coupon?.couponUseConditionsType === 'UNLIMITED'">
                      {{ authStore.i18n("cm_coupon.noLimit") }}
                    </div>
                  </div>
                  <div
                    v-if="
                      coupon?.couponWay === 'COUPON_WAY_DISCOUNT' &&
                      coupon?.preferentialAmount
                    "
                    class="text-[0.24rem] leading-[0.24rem] flex items-center mt-[0.14rem]"
                  >
                    {{ authStore.i18n("cm_coupon.upToMoney") }}
                    {{ setNewUnit(coupon?.preferentialAmount) }}
                  </div>
                  <div
                    v-if="coupon?.availableFlag === false"
                    class="text-[0.24rem] leading-[0.24rem] flex items-center mt-[0.14rem]"
                  >
                    <img
                      loading="lazy"
                      src="@/assets/icons/tipGray.svg"
                      alt="tip"
                      class="w-[0.24rem]"
                      referrerpolicy="no-referrer"
                    />
                    {{ coupon?.notAvailableReason }}
                  </div>
                </div>
              </div>
            </n-checkbox>
          </n-checkbox-group>
        </n-scrollbar>
        <div class="px-[0.16rem] py-[0.2rem] flex justify-between items-center">
          <span class="text-[0.28rem] leading-[0.28rem] font-medium"
            >{{ authStore.i18n("cm_order.existingDiscount") }}:</span
          >
          <span class="text-[0.36rem] leading-[0.36rem] text-[#e50113]">{{
            setUnit(pageData.chooseCouponAmount)
          }}</span>
        </div>
        <div
          class="px-[0.16rem] my-[0.16rem] w-full flex justify-between items-center"
        >
          <n-button
            color="#F6D2D4"
            text-color="#E50113"
            @click="onChooseCouponCancel"
            class="flex-1 rounded-[10rem] h-[0.8rem] text-[0.32rem] mr-[0.24rem] font-medium"
          >
            {{ authStore.i18n("cm_order.orderCancel") }}
          </n-button>
          <n-button
            color="#E50113"
            text-color="#fff"
            @click="onChooseCouponConfirm"
            class="flex-1 rounded-[10rem] h-[0.8rem] text-[0.32rem] font-medium"
          >
            {{ authStore.i18n("cm_order.orderConfirm") }}
          </n-button>
        </div>
      </div>
    </n-drawer>

    <!-- 订单取消 -->
    <order-cancel
      ref="orderCancelRef"
      @updateOrderState="onUpdateOrderState"
    ></order-cancel>

    <!-- 订单支付协议 -->
    <order-pay-agree
      ref="orderPayAgreeRef"
      @onHasReadFinished="onHasReadFinished"
      @onUpdateAcceptTerms="onUpdateAcceptTerms"
    >
    </order-pay-agree>
  </div>
</template>

<script setup lang="ts">
import _ from "lodash";
import { useAuthStore } from "@/stores/authStore";
import OrderCancel from "@/pages/h5/order/components/OrderCancel.vue";
import CouponDetail from "@/pages/h5/order/components/CouponDetail.vue";
import OrderPayAgree from "@/pages/h5/order/components/OrderPayAgree.vue";
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const orderCancelRef = ref<any>(null);
const orderPayAgreeRef = ref<any>(null);

const pageData = reactive(<any>{
  orderNo: route?.query?.orderNo || "", //订单号
  preview: route?.query?.preview || false, //是否来自admin的预览
  mallOrderStatus: null, // 订单状态
  statusDesc: null, // 订单状态描述
  orderTime: null, // 下单时间
  orderRemark: "", // 客户备注
  merChantRemark: "", //商家备注
  payType: "", // 支付方式 (线上支付或线下支付)
  payMode: "", // 支付模式 (一次性支付或分开支付)
  quotationMode: null, //报价模式
  addressInfo: <any>{}, // 地址信息
  boxList: <any>[], // 装箱列表
  totalCount: null, //sku总数量
  totalAmount: null, //sku总价值
  transportAmountList: <any>[], //运输线路与预估费用列表
  transportId: null, //选中的运输线路id
  currentTransport: {}, //当前选中的运输线路 需根据这个线路计算 装箱信息的箱运费、到手单价以及实际支付金额
  expandedTransport: [], // 默认展开的运输线路列表
  showLoadTrans: false, // 是否展示估计装运的介绍
  showLogDetails: false, // 是否展示物流信息
  showComDetails: false, // 是否展示费用明细
  showPayInfoError: false, // 是否展示付款信息错误提示
  showCompositeAmount: false, // 控制实际支付金额提示内容 true 显示为（产品成本 + 国际费用），false 显示为（产品成本）
  feeDetailsList: <any>[], //展示的费用明细
  // showAmountDetails: false, //是否展示付款信息
  productAmount: <any>{}, //产品成本
  paymentAmount: null, //订单总价 前端根据报价模式 支付模式计算
  paymentAmountMessage: null, // 订单总价提示
  actualPaymentAmount: 0, // 实际支付金额(订单总价-优惠金额)
  couponInfo: <any>{
    productCouponAmount: 0, //产品优惠的金额
    productCouponIds: <any>[], //产品优惠券id列表
    productCouponList: <any>[], //产品优惠券列表
    commissionCouponAmount: 0, //佣金优惠的金额
    commissionCouponIds: [], //佣金优惠券id列表
    commissionCouponList: <any>[], //佣金优惠券列表
  }, //优惠券信息（包含产品和佣金优惠券列表及对应的优惠金额）
  discountedAmount: 0, // 优惠金额
  showCouponChoose: false,
  chooseCouponType: null, // 当前弹窗打开的优惠券类型
  chooseCouponList: null, // 选择的优惠券列表
  chooseCouponAmount: 0, // 选择的优惠券列表里选中的优惠券优惠金额的总计
  selectedCouponIds: [],
  chooseCouponLoading: false,
  allCouponError: false, //产品/佣金都错误
  productCouponError: false, //产品券信息错误
  commissionCouponError: false, // 佣金券信息错误
  showCouponInfoError: false, //点击付款 是否展示优惠券信息错误提示

  productCouponAmount: <any>{}, // 产品券费用明细
  comissCouponAmount: <any>{}, // 佣金券费用明细
  productAddComissSumAmount: null, //付款成功后的产品券+佣金券优惠总金额
  paidAmount: null, //实付款
  hasReadFinished: false, // 是否阅读完支付协议
  acceptTerms: false, // 是否确认协议
  sourcePlatform: "", // 订单来源平台
  supportOnlinePay: true, // 是否支持在线支付
});

const isOrderCanceled = computed(() => {
  return pageData.mallOrderStatus === "MALL_CANCELED";
});

// 线下支付
const isPayOffline = computed(() => {
  return pageData.payType === "ONLINE_PAY_OFFLINE";
});

// 待支付订单费用
const isPayAllFee = computed(() => {
  return pageData.mallOrderStatus === "MALL_WAIT_PAY_ALL_FEE";
});

// 待支付产品成本
const isPayProduct = computed(() => {
  return pageData.mallOrderStatus === "MALL_WAIT_PAY_PRODUCT";
});

// 待支付国际费用
const isPayInterFee = computed(() => {
  return pageData.mallOrderStatus === "MALL_WAIT_PAY_INTER_FEE";
});

// 待支付费用
const isPayFee = computed(() => {
  return isPayAllFee.value || isPayProduct.value || isPayInterFee.value;
});

// 用户分开支付，支付产品成本后，已付款明细只展示产品成本，国际费用隐藏
const hideInterFeeDisplay = computed(() => {
  return (
    pageData.payMode === "PAY_MODE_PART" &&
    (pageData.quotationMode === "QUOTATION_MODE_DDP" ||
      pageData.quotationMode === "QUOTATION_MODE_CIF") &&
    (pageData.mallOrderStatus === "MALL_PURCHASING" ||
      pageData.mallOrderStatus === "MALL_WAIT_CAL_INTER_FEE" ||
      pageData.mallOrderStatus === "MALL_WAIT_PAY_INTER_FEE")
  );
});

onGetOrderDetail();
async function onGetOrderDetail() {
  const res: any = await useGetOrderDetail({
    orderNo: pageData.orderNo,
    isAdminPreview: pageData.preview || false, //是否来自admin的预览
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    pageData.transportAmountList.sort((a: any, b: any) => a.amount - b.amount);
    pageData.transportAmountList.forEach((item: any) => {
      pageData.expandedTransport.push(item.transportId);
    });

    // 待支付订单请求可用优惠券列表
    if ((isPayAllFee.value || isPayProduct.value) && !isPayOffline.value) {
      onGetCouponList("init");
    }

    onUpdateTrans();
    onInitPaymentAmount();
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

async function onGetCouponList(init?: any) {
  const res: any = await useGetCouponUsableList({ orderNo: pageData.orderNo });
  if (res?.result?.code === 200) {
    if (pageData.productCouponError === true) {
      pageData.couponInfo.productCouponList = res?.data.productCouponList;
      return;
    } else if (pageData.commissionCouponError === true) {
      pageData.couponInfo.commissionCouponList = res?.data.commissionCouponList;
      return;
    }
    pageData.couponInfo = res?.data;
    if (init) {
      onCheckCouponInfo();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res.result?.message);
  }
}

// 校验该订单的优惠券是否被锁定
function onCheckCouponInfo() {
  let isLocked = false; // 标记是否存在锁定状态的优惠券
  const couponLists = [
    {
      list: pageData.couponInfo.productCouponList,
      type: "COUPON_TYPE_PRODUCT",
    },
    {
      list: pageData.couponInfo.commissionCouponList,
      type: "COUPON_TYPE_COMMISSION",
    },
  ];

  // 检查是否存在锁定状态的优惠券
  isLocked = couponLists.some(({ list }) =>
    list.some((coupon: any) => coupon.ticketStatus === "TICKET_LOCK")
  );

  // 如果存在锁定状态，才进行计算
  if (isLocked) {
    couponLists.forEach(({ list, type }) => {
      let totalPreferentialAmount = 0; // 累计优惠金额
      const selectedCouponIds: string[] = [];
      const chooseCouponList: any[] = [];

      // 遍历优惠券，计算选中优惠券的信息
      list.forEach((coupon: any) => {
        if (coupon.check) {
          totalPreferentialAmount += coupon.ticketActualPrice || 0;
          selectedCouponIds.push(coupon.id);
          chooseCouponList.push(_.cloneDeep(coupon));
        }
      });

      // 更新对应的 pageData 数据
      if (type === "COUPON_TYPE_PRODUCT") {
        pageData.couponInfo.productCouponAmount = -totalPreferentialAmount;
        pageData.couponInfo.productCouponIds = _.cloneDeep(selectedCouponIds);
      } else if (type === "COUPON_TYPE_COMMISSION") {
        pageData.couponInfo.commissionCouponAmount = -totalPreferentialAmount;
        pageData.couponInfo.commissionCouponIds =
          _.cloneDeep(selectedCouponIds);
      }
    });
    onUpdatePaymentAmount();
  }
}

// 根据运输线路 计算箱运费、到手单价、订单总价、实际支付金额
// 有运输线路且使用单箱运费模式展示到手单价、箱运费
function onUpdateTrans(val?: any) {
  if (!pageData.transportAmountList.length) return;
  let matchTrans;
  if (val) {
    matchTrans = pageData.transportAmountList.find(
      (item: any) => item.transportId === val
    );
    pageData.transportId = val;
    window?.MyStat?.addPageEvent(
      "payment_choice_route",
      `选择线路：${matchTrans.name}`
    );
  } else {
    matchTrans = pageData.transportAmountList[0];
    pageData.transportId = matchTrans.transportId;
  }

  if (matchTrans) {
    pageData.currentTransport = matchTrans;
    // 更新每个箱子的箱运费
    if (pageData.transportAmountType === 1) {
      pageData.boxList.forEach((box: any) => {
        const matchedFee = box.boxTransportFeeList.find(
          (fee: any) => fee.transportId === matchTrans.transportId
        );
        if (matchedFee) {
          box.boxFee = matchedFee.amount;
        }
        // 更新每个sku的到手单价(销售单价+单个商品分摊的运费，单个商品分摊运费=单箱运费/单箱装箱数，除不尽四舍五入，保留两位小数)
        box.skuList.forEach((sku: any) => {
          const boxFeePerUnit = (box.boxFee * 100) / box.skuCount / 100;
          sku.receivedUnitPrice = (
            Number(boxFeePerUnit) + Number(sku.unitPrice)
          ).toFixed(2);
        });
      });
    }

    //更新订单总价(支付模式为一次性支付时，产品成本 + 国际费用) 、实际支付金额
    if (pageData.payMode === "PAY_MODE_ALL") {
      pageData.paymentAmount = mathRound(
        pageData.productAmount?.amount + pageData.currentTransport?.amount
      );
      pageData.actualPaymentAmount = mathRound(
        pageData.paymentAmount + pageData.discountedAmount
      );
    }
  }
}

/**
 * 计算订单总价,实际支付金额
 * 报价模式为 DDP或者 CIF 时
 * 支付模式为 分开支付：
 *    - 支付产品成本时，订单总价为产品成本；提示为（产品成本）
 *    - 支付国际费用时，订单总价为国际费用。提示为（国际费用）
 * 支付模式为 一次性支付时：
 *    - 订单总价为产品成本 + 国际费用。提示为（产品成本 + 国际费用）
 *
 * 其他报价模式：
 *    - 一次性支付时，订单总价为产品成本。提示为（产品成本）
 */
function onInitPaymentAmount() {
  const { payMode, quotationMode, productAmount, currentTransport } = pageData;

  // 报价模式为 DDP 或 CIF 时
  if (
    quotationMode === "QUOTATION_MODE_DDP" ||
    quotationMode === "QUOTATION_MODE_CIF"
  ) {
    // 支付模式为一次性支付时，产品成本 + 国际费用
    if (payMode === "PAY_MODE_ALL") {
      pageData.paymentAmount = mathRound(
        productAmount?.amount + currentTransport?.amount,
        2
      );
      pageData.paymentAmountMessage = `${authStore.i18n(
        "cm_order.productCost"
      )} + ${authStore.i18n("cm_order.interFees")}`;
    } else {
      // 支付模式为分开支付时，根据订单状态决定支付金额
      if (isPayProduct.value) {
        pageData.paymentAmount = productAmount?.amount;
        pageData.paymentAmountMessage = authStore.i18n("cm_order.productCost");
      } else if (isPayInterFee.value) {
        pageData.paymentAmount = currentTransport?.amount;
        pageData.paymentAmountMessage = authStore.i18n("cm_order.interFees");
      }
    }
  } else {
    // 其他报价模式时，支付金额为产品成本
    pageData.paymentAmount = productAmount?.amount;
    pageData.paymentAmountMessage = authStore.i18n("cm_order.productCost");
  }
  // 更新实际支付金额
  pageData.actualPaymentAmount = mathRound(
    pageData.paymentAmount + pageData.discountedAmount
  );
}

function getSkuName(specs: any) {
  const names = specs?.map((spec: any) => {
    return `${spec.specName}: ${spec.itemName}`;
  });
  return names?.join("; ");
}

function onOrderRemarkTrack() {
  window?.MyStat?.addPageEvent(
    "payment_input_buyer_msg",
    `输入买家留言：${pageData.orderRemark}`
  );
}

// 点击付款 跳转至收银台
async function onOrderPay(event?: any) {
  // 未确认协议，弹出协议弹框
  if (!pageData.acceptTerms) {
    window?.MyStat?.addPageEvent(
      "payment_open_terms",
      `打开支付协议对话框（付款时未勾选协议，弹出协议对话框）`
    ); // 埋点
    orderPayAgreeRef?.value?.onOpenAgree();
    return;
  }

  let paymentId;
  const res: any = await useOpenCashDesk({
    orderNo: pageData.orderNo,
    amount: pageData.actualPaymentAmount,
    orderRemark: pageData.orderRemark,
    transportId: pageData.transportId,
    productIdsList: pageData.couponInfo.productCouponIds,
    commissionIdsList: pageData.couponInfo.commissionCouponIds,
  });
  if (res?.result?.code === 200) {
    paymentId = res?.data?.paymentId;
    navigateToPage(
      "/h5/order/payment",
      {
        orderNo: pageData.orderNo,
        paymentId,
      },
      false,
      event
    );
  } else {
    window?.MyStat?.addPageEvent(
      "order_detail_gopay_fail",
      `订单详情页去支付失败：${res?.result?.message}`
    );
    if (res?.result?.code === 81005) {
      pageData.showPayInfoError = true;
    } else if (res?.result?.code === 70114) {
      //产品券信息错误
      pageData.showCouponInfoError = true;
      pageData.productCouponError = true;
      pageData.couponInfo.productCouponIds = [];
      pageData.couponInfo.productCouponAmount = 0;
      onUpdatePaymentAmount();
      onGetCouponList();
    } else if (res?.result?.code === 70115) {
      //佣金券信息错误
      pageData.showCouponInfoError = true;
      pageData.commissionCouponError = true;
      pageData.couponInfo.commissionCouponIds = [];
      pageData.couponInfo.commissionCouponAmount = 0;
      onUpdatePaymentAmount();
      onGetCouponList();
    } else if (res?.result?.code === 70116) {
      //产品/佣金都错误
      pageData.showCouponInfoError = true;
      pageData.allCouponError = true;
      pageData.couponInfo.productCouponIds = [];
      pageData.couponInfo.productCouponAmount = 0;
      pageData.couponInfo.commissionCouponIds = [];
      pageData.couponInfo.commissionCouponAmount = 0;
      onUpdatePaymentAmount();
      onGetCouponList();
    } else {
      showToast(res?.result?.message);
    }
  }
}

// 点击付款 报错提示
function onClosePayError() {
  pageData.showPayInfoError = false;
  window.location.reload();
}

// 取消订单
function onOrderCancel() {
  orderCancelRef.value?.onOpenDialog(pageData.orderNo);
}

// 取消订单后, 订单状态更新
function onUpdateOrderState() {
  window.location.reload();
}

// 展示物流信息
function onOpenLogDetail() {
  pageData.showLogDetails = true;
}

// 展示估计装运的介绍
function onOpenLoadTrans() {
  pageData.showLoadTrans = true;
}

// 展示费用明细
function onShowFeeDetails(val: any) {
  pageData.feeDetailsList = val;
  pageData.showComDetails = true;
}

// // 展示付款信息
// function openAmountDetails() {
//   pageData.showAmountDetails = !pageData.showAmountDetails;
// }

// 返回
function onBackClick() {
  router.go(-1);
}

// 打开优惠券选择弹框
function onChooseCoupon(type: any) {
  // 重置数据
  pageData.selectedCouponIds = [];
  pageData.chooseCouponList = [];
  pageData.chooseCouponAmount = 0;
  pageData.chooseCouponType = type;
  pageData.showCouponChoose = true;

  if (type === "COUPON_TYPE_PRODUCT") {
    pageData.selectedCouponIds = _.cloneDeep(
      pageData.couponInfo.productCouponIds || []
    );
    pageData.chooseCouponList = _.cloneDeep(
      pageData.couponInfo.productCouponList || []
    );
    pageData.chooseCouponAmount = _.cloneDeep(
      pageData.couponInfo.productCouponAmount || 0
    );
  } else if (type === "COUPON_TYPE_COMMISSION") {
    pageData.selectedCouponIds = _.cloneDeep(
      pageData.couponInfo.commissionCouponIds || []
    );
    pageData.chooseCouponList = _.cloneDeep(
      pageData.couponInfo.commissionCouponList || []
    );
    pageData.chooseCouponAmount = _.cloneDeep(
      pageData.couponInfo.commissionCouponAmount || 0
    );
  }
  window?.MyStat?.addPageEvent(
    "order_open_coupon_list",
    `打开${
      pageData.chooseCouponType === "COUPON_TYPE_PRODUCT" ? "产品" : "佣金"
    }优惠券列表`
  ); // 埋点
}

// 重新选择优惠券
function onChooseCouponAgain() {
  pageData.showCouponInfoError = false;
  if (pageData.productCouponError || pageData.allCouponError) {
    onChooseCoupon("COUPON_TYPE_PRODUCT");
  } else {
    onChooseCoupon("COUPON_TYPE_COMMISSION");
  }
}

// 优惠券选择
async function onCheckAvailableList() {
  pageData.chooseCouponLoading = true;
  // 选中的优惠券
  const selectCouponModelsList = pageData.selectedCouponIds.map((id: any) => {
    const coupon = pageData.chooseCouponList.find(
      (coupon: any) => coupon.id === id
    );
    if (coupon && !coupon.check) {
      window?.MyStat?.addPageEvent(
        "order_select_coupon",
        `选择优惠券：${coupon.couponName}`
      ); //埋点
    }
    if (coupon) {
      coupon.check = true; // 标记优惠券为已选中
    }
    return coupon;
  });

  // 未选中的优惠券
  const notCouponModelsList = pageData.chooseCouponList.filter(
    (coupon: any) => {
      const isNotSelected = !pageData.selectedCouponIds.includes(coupon.id);
      if (isNotSelected && coupon.check) {
        window?.MyStat?.addPageEvent(
          "order_unselect_coupon",
          `取消选择优惠券：${coupon.couponName}`
        ); //埋点
      }
      if (isNotSelected) {
        coupon.check = false; // 标记为未选中
      }
      return isNotSelected;
    }
  );

  const res: any = await useCheckAvailableList({
    orderNo: pageData.orderNo,
    selectCouponModelsList,
    notCouponModelsList,
    couponType: pageData.chooseCouponType,
  });
  pageData.chooseCouponLoading = false;
  if (res?.result?.code === 200) {
    let totalDiscountAmount = 0;
    pageData.chooseCouponList.forEach((coupon: any) => {
      const matchedCoupon = res?.data?.find(
        (item: any) => item.id === coupon.id
      );

      if (matchedCoupon) {
        // 更新优惠券的 check, availableFlag, ticketActualPrice
        coupon.check = matchedCoupon.check;
        coupon.availableFlag = matchedCoupon.availableFlag;
        coupon.ticketActualPrice = matchedCoupon.ticketActualPrice;
        coupon.notAvailableReason = matchedCoupon.notAvailableReason;

        // 如果优惠券的check为 true，累计优惠金额
        if (coupon.check) {
          totalDiscountAmount += coupon.ticketActualPrice;
        }

        // 如果优惠券的check为false 且该id在selectedCouponIds中，则从selectedCouponIds中移除该优惠券的 id
        if (!coupon.check && pageData.selectedCouponIds.includes(coupon.id)) {
          pageData.selectedCouponIds = pageData.selectedCouponIds.filter(
            (id: any) => id !== coupon.id
          );
          window?.MyStat?.addPageEvent(
            "order_unselect_coupon",
            `自动取消选择优惠券：${coupon.couponName}`
          ); // 埋点
        }
      }
    });

    pageData.chooseCouponAmount = -totalDiscountAmount;
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res.result?.message);
  }
}

// 优惠券选择确认
function onChooseCouponConfirm() {
  if (pageData.chooseCouponType === "COUPON_TYPE_PRODUCT") {
    pageData.couponInfo.productCouponAmount = pageData.chooseCouponAmount;
    pageData.couponInfo.productCouponList = pageData.chooseCouponList;
    pageData.couponInfo.productCouponIds = pageData.selectedCouponIds;
  } else if (pageData.chooseCouponType === "COUPON_TYPE_COMMISSION") {
    pageData.couponInfo.commissionCouponAmount = pageData.chooseCouponAmount;
    pageData.couponInfo.commissionCouponList = pageData.chooseCouponList;
    pageData.couponInfo.commissionCouponIds = pageData.selectedCouponIds;
  }
  window?.MyStat?.addPageEvent(
    "order_confirm_coupon",
    `确认${
      pageData.chooseCouponType === "COUPON_TYPE_PRODUCT" ? "产品" : "佣金"
    }优惠券，共${pageData.selectedCouponIds.length}张，优惠金额：${setUnit(
      -pageData.chooseCouponAmount
    )}`
  ); // 埋点
  onUpdatePaymentAmount();
  pageData.showCouponChoose = false;
}

// 计算优惠金额,计算实际金额
function onUpdatePaymentAmount() {
  // 计算优惠金额
  pageData.discountedAmount = mathRound(
    (pageData.couponInfo.productCouponAmount || 0) +
      (pageData.couponInfo.commissionCouponAmount || 0)
  );
  // 更新实际支付金额
  pageData.actualPaymentAmount = mathRound(
    pageData.paymentAmount + pageData.discountedAmount
  );
}

function onChooseCouponCancel() {
  window?.MyStat?.addPageEvent(
    "order_close_coupon_list",
    `关闭${
      pageData.chooseCouponType === "COUPON_TYPE_PRODUCT" ? "产品" : "佣金"
    }优惠券列表`
  ); // 埋点
  pageData.showCouponChoose = false;
}

function onOpenAgreeModal() {
  window?.MyStat?.addPageEvent(
    "payment_open_terms",
    `打开支付协议对话框(点击支付协议，弹出协议对话框)`
  ); // 埋点
  orderPayAgreeRef?.value?.onOpenAgree();
}

function onHasReadFinished() {
  pageData.hasReadFinished = true;
}

function onUpdateAcceptTerms(val?: any) {
  // 阅读完协议后，即可确认协议
  if (pageData.hasReadFinished) {
    pageData.acceptTerms = val;
    if (pageData.acceptTerms) {
      window?.MyStat?.addPageEvent("payment_agree_terms", `同意支付协议`); // 埋点
    } else {
      window?.MyStat?.addPageEvent(
        "payment_unselect_terms",
        `取消选择支付协议`
      ); // 埋点
    }
    return;
  }
  window?.MyStat?.addPageEvent(
    "payment_open_terms",
    `打开支付协议对话框(未阅读完协议，点击勾选框，弹出协议对话框)`
  ); // 埋点
  orderPayAgreeRef?.value?.onOpenAgree();
}
</script>
<style scoped lang="scss">
.goodsName-ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
}

:deep(.n-tabs .n-tabs-nav-scroll-wrapper) {
  background: #fff;
  margin: 0.12rem 0.2rem;
  border-radius: 0.1rem;
  padding: 0 0.2rem;
}
:deep(.n-tabs .n-tab-pane) {
  padding: 0;
}
.order-state {
  width: 100%;
  height: 1.1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 0.3rem;
  background-size: 100%100%;
  background-image: url("https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/23/bba16129-c477-4094-b0b4-5cf7e892b306.png");
}
.order-card {
  padding: 0.32rem 0.16rem;
  background: #fff;
  border-radius: 0.12rem;
  margin-top: 0.2rem;
  margin-bottom: 0.2rem;
}

.transport-box {
  width: 100%;
  border: 0.02rem solid #d7d7d7;
  margin-top: 0.2rem;
  margin-bottom: 0.12rem;
  padding: 0.24rem;
  border-radius: 0.12rem;
}
:deep(.n-radio__label) {
  font-size: 0.32rem;
}

.page-footer {
  width: 100%;
  min-height: 1.28rem;
  background: #fff;
  border-radius: 0.12rem;
  border: 0.02rem solid #e2e2e2;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.32rem 0.16rem;
  z-index: 9;
  display: flex;
  flex-direction: column;
  align-items: end;
}
:deep(.n-radio--checked) {
  --n-text-color-disabled: rgb(51, 54, 57) !important;
  --n-dot-color-disabled: #e50113 !important;
  --n-box-shadow-disabled: inset 0 0 0 0.02rem #e50113 !important;
}

.coupon-card {
  color: #333;
  .coupon-border {
    border-color: #333;
  }
  :deep(.n-checkbox__label) {
    color: #333;
  }
}

.checked-coupon {
  color: #e50113 !important;
  .coupon-border {
    border-color: #e50113;
  }
  :deep(.n-checkbox__label) {
    color: #e50113 !important;
  }
}

.disabled-coupon {
  color: #7f7f7f;
  .coupon-border {
    border-color: #7f7f7f;
  }
  :deep(.n-checkbox__label) {
    color: #7f7f7f;
  }
}

.loading-overlay {
  position: absolute;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
.coupon-wrapper {
  :deep(.n-checkbox-box) {
    width: 0.36rem;
    height: 0.36rem;
    border-radius: 50%;
  }

  :deep(.n-checkbox__label) {
    width: calc(100% - 0.48rem);
  }
  :deep(.n-checkbox-box-wrapper) {
    order: 2; /* 将勾选框放到右侧 */
  }
}

:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}

:deep(.agree-checkbox.n-checkbox .n-checkbox-box) {
  background-color: #f2f2f2;
}

:deep(.agree-checkbox .n-checkbox-box__border) {
  border: 0.02rem solid #666;
}

:deep(.agree-checkbox.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background-color: #e50113 !important;
}
</style>
